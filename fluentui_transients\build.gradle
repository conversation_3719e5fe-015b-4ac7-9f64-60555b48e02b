/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

apply from: '../config.gradle'
apply from: '../publish.gradle'

android {
    compileSdkVersion constants.compileSdkVersion
    buildFeatures {
        viewBinding true
    }
    defaultConfig {
        minSdkVersion constants.minSdkVersion
        targetSdkVersion constants.targetSdkVersion
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.microsoft.fluentui.transients'
    lint {
        abortOnError false
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':fluentui_core')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "com.google.android.material:material:$materialVersion"
    androidTestImplementation "androidx.test.ext:junit:$extJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$espressoVersion"
}

tasks.register('sourceJar', Jar) {
    from android.sourceSets.main.java.srcDirs
    archiveClassifier.set("sources")
}

project.afterEvaluate {
    project.ext.publishingFunc('fluentui_transients')
}
