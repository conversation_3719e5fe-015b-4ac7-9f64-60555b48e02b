<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET"/>

    <application
        android:allowBackup="true"
        android:icon="@mipmap/fluent_logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/fluent_logo"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".V2DemoListActivity"
            android:exported="true"
            android:label="@string/app_title">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Demo activities -->
        <activity android:name="com.microsoft.fluentuidemo.demos.V2AcrylicPaneActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ActionBarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2AppBarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2AvatarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2AvatarCarouselActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2AvatarGroupActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BadgeActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ButtonsActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BannerActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BasicChipActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BasicControlsActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BottomDrawerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:windowSoftInputMode="adjustResize"
            android:enableOnBackInvokedCallback="true"/>
        <activity android:name="com.microsoft.fluentuidemo.demos.V2BottomSheetActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"/>
        <activity android:name="com.microsoft.fluentuidemo.demos.V2CardActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2CardNudgeActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2CitationActivity" />
        <activity
            android:name="com.microsoft.fluentuidemo.demos.V2ContextualCommandBarActivity"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2DialogActivity" />
        <activity android:name="com.microsoft.fluentuidemo.V2DesignTokensActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2DrawerActivity"/>
        <activity android:name="com.microsoft.fluentuidemo.demos.V2LabelActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ListItemActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2MenuActivity"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2PeoplePickerActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2PersonaActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2PersonaChipActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2PersonaListActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ProgressActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ScaffoldActivity" />
        <activity
            android:name="com.microsoft.fluentuidemo.demos.V2SearchBarActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2SegmentedControlActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ShimmerActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2SideRailActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2SnackbarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2TabBarActivity" />
        <activity
            android:name="com.microsoft.fluentuidemo.demos.V2TextFieldActivity"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.microsoft.fluentuidemo.demos.V2ToolTipActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.ActionBarLayoutActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.AppBarLayoutActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.AvatarViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.AvatarGroupViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.BasicInputsActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.BottomNavigationActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.BottomSheetActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.CalendarViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.ContextualCommandBarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.DateTimePickerActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.DrawerActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.ListItemViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.actionbar.V2ActionBarDemoActivity" />
        <activity
            android:name="com.microsoft.fluentuidemo.demos.PeoplePickerViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.microsoft.fluentuidemo.demos.PersistentBottomSheetActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.PersonaChipViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.PersonaListViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.PersonaViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.PopupMenuActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.ProgressActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.SnackbarActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.TabLayoutActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.TemplateViewActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.TooltipActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.TypographyActivity" />
        <activity android:name="com.microsoft.fluentuidemo.demos.actionbar.ActionBarDemoActivity" />

    </application>

</manifest>