/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

apply from: '../config.gradle'
apply from: '../publish.gradle'

android {
    compileSdkVersion constants.compileSdkVersion
    defaultConfig {
        minSdkVersion constants.minSdkVersion
        targetSdkVersion constants.targetSdkVersion
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion composeCompilerVersion
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.microsoft.fluentui.listitem'
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':fluentui_core')
    implementation project(':fluentui_icons')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.constraintlayout:constraintlayout:$constraintLayoutVersion"

    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "androidx.recyclerview:recyclerview:$recyclerViewVersion"

    implementation("androidx.compose.foundation:foundation")
    implementation("androidx.compose.material:material")
    implementation("androidx.compose.runtime:runtime")
    implementation("androidx.compose.ui:ui")
    implementation "androidx.constraintlayout:constraintlayout-compose:$constraintLayoutComposeVersion"
}

tasks.register('sourceJar', Jar) {
    from android.sourceSets.main.java.srcDirs
    archiveClassifier.set("sources")
}

project.afterEvaluate {
    project.ext.publishingFunc('fluentui_listitem')
}
