/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

apply from: '../config.gradle'
apply from: '../publish.gradle'

android {
    compileSdkVersion constants.compileSdkVersion
    defaultConfig {
        minSdkVersion constants.minSdkVersion
        targetSdkVersion constants.targetSdkVersion
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildscript {
        repositories {
            mavenCentral()
        }
    }
    lint {
        baseline = file("lint-baseline.xml")
        abortOnError false
    }
    namespace 'com.microsoft.fluentui_container'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':fluentui_calendar')
    api project(':fluentui_ccb')
    api project(':fluentui_controls')
    api project(':fluentui_core')
    api project(':fluentui_drawer')
    api project(':fluentui_icons')
    api project(':fluentui_listitem')
    api project(':fluentui_menus')
    api project(':fluentui_notification')
    api project(':fluentui_others')
    api project(':fluentui_peoplepicker')
    api project(':fluentui_persona')
    api project(':fluentui_progress')
    api project(':fluentui_tablayout')
    api project(':fluentui_topappbars')
    api project(':fluentui_transients')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "com.google.android.material:material:$materialVersion"
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$extJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$espressoVersion"

    //Compose BOM
    implementation platform("androidx.compose:compose-bom:$composeBomVersion")
}

tasks.register('sourceJar', Jar) {
    from android.sourceSets.main.java.srcDirs
    archiveClassifier.set("sources")
}

project.afterEvaluate {
    project.ext.publishingFunc('FluentUIAndroid')
}

