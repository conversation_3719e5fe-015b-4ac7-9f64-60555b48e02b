/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

apply from: '../config.gradle'
apply from: '../publish.gradle'

android {
    compileSdkVersion constants.compileSdkVersion
    buildFeatures {
        viewBinding true
        compose true
    }
    defaultConfig {
        minSdkVersion constants.minSdkVersion
        targetSdkVersion constants.targetSdkVersion
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'
        vectorDrawables.useSupportLibrary = true
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    composeOptions {
        kotlinCompilerExtensionVersion composeCompilerVersion
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    namespace 'com.microsoft.fluentui.topappbars'
    lint {
        abortOnError false
    }
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':fluentui_core')
    implementation project(':fluentui_icons')
    implementation project(':fluentui_persona')
    implementation project(':fluentui_progress')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "com.google.android.material:material:$materialVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleComposeVersion"

    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-util"
    implementation "androidx.compose.material:material"

    androidTestImplementation "androidx.test.ext:junit:$extJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$espressoVersion"
}

tasks.register('sourceJar', Jar) {
    from android.sourceSets.main.java.srcDirs
    archiveClassifier.set("sources")
}

project.afterEvaluate {
    project.ext.publishingFunc('fluentui_topappbars')
}